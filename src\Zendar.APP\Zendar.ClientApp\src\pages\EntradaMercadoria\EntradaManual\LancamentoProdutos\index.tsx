import { But<PERSON>, Box, Stack, useMediaQuery } from '@chakra-ui/react';
import { useCallback, useState, useMemo, useEffect, useRef } from 'react';
import { toast } from 'react-toastify';

import {
  buscarVariacoesProduto,
  CorOption,
  TamanhoOption,
} from 'helpers/api/Produto';
import { formatQueryPagegTable } from 'helpers/format/formatQueryParams';
import useWindowSize from 'helpers/layout/useWindowSize';

import api, { ResponseApi } from 'services/api';

import { useEntradaMercadoriaDadosCadastroContext } from 'store/EntradaMercadoria/EntradaMercadoriaDadosCadastro';
import { useEntradaMercadoriaEtapasContext } from 'store/EntradaMercadoria/EntradaMercadoriaEtapas';
import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import TotalizadoresFixos from 'pages/EntradaMercadoria/Importacao/Continuar/VincularProdutos/components/TotalizadoresFixos';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ModalConfirmacaoExcluir } from 'components/Modal/ModalConfirmacaoExcluir';
import {
  Container,
  Body,
  Footer,
  StepDescriptionAccordion,
} from 'components/update/Steps/StepContent';

import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';

import { ListagemLancamentoManual } from './components/ListagemLancamentoManual';
import { useProdutosLancamento } from './hooks/useProdutosLancamento';
import { ModalAdicionarProduto } from './ModalAdicionarProduto';
import { ModalEditarProduto } from './ModalEditarProduto';
import {
  ObterInformacoesProdutosResponse,
  ProdutoResponse,
  AdicionarProdutoRetorno,
  ProdutoObterResponse,
} from './types';

export function LancamentoProdutos() {
  const { casasDecimais } = usePadronizacaoContext();
  const { nextStep, previousStep } = useEntradaMercadoriaEtapasContext();
  const {
    entradaMercadoriaId,
    descartarEntradaMercadoria,
    voltarParaListagem,
    temPermissaoExcluir,
    isReadOnly,
    IsCadastroExterno,
    statusLancamentos: { foiLancadoEstoque, foiLancadoFinanceiro },
    menuIsOpen,
  } = useEntradaMercadoriaDadosCadastroContext();

  const [isLoadingOperacoes, setIsLoadingOperacoes] = useState(false);
  const [entradaRateiaIcmsSt, setEntradaRateiaIcmsSt] = useState(false);
  const [isLargerThan900] = useMediaQuery('(min-width: 900px)');
  const { height: windowHeight } = useWindowSize();

  // Hook do React Query para gerenciar produtos
  const {
    produtos,
    informacoesRodape,
    isLoading,
    buscarMaisItensNoScroll,
    handleToggleLinhaProduto,
    isOpen,
    recarregarLista,
  } = useProdutosLancamento(entradaMercadoriaId || '');

  const maxContainerHeight = useMemo((): string => {
    const stepDescriptionHeight = 80;
    const containerPadding = 48;
    const totalizadoresHeight = informacoesRodape.totalProdutos > 0 ? 155 : 0;
    const footerHeight = isLargerThan900 ? 70 : 0;
    const marginBottom = 24;

    const availableHeight =
      windowHeight -
      stepDescriptionHeight -
      containerPadding -
      totalizadoresHeight -
      footerHeight -
      marginBottom;

    const dynamicHeight = Math.max(availableHeight, 300);

    return `${dynamicHeight}px`;
  }, [windowHeight, isLargerThan900, informacoesRodape.totalProdutos]);

  const handleDescartarEntradaMercadoria = async () => {
    setIsLoadingOperacoes(true);
    await descartarEntradaMercadoria();
    setIsLoadingOperacoes(false);
  };

  const adicionarProduto = useCallback(
    async (produtoSendoAdicionado: ProdutoResponse) => {
      setIsLoadingOperacoes(true);
      const response = await api.post<
        void,
        ResponseApi<AdicionarProdutoRetorno[]>
      >(ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_ADICIONAR_ITENS, {
        entradaMercadoriaId,
        ...produtoSendoAdicionado,
      });

      if (response) {
        if (response.avisos) {
          response.avisos.map((aviso: string) => toast.warning(aviso));
        }

        if (response.sucesso) {
          await new Promise((resolve) => setTimeout(resolve, 100));
          // Recarregar a lista após adicionar produto
          await recarregarLista();
        }
      }
      setIsLoadingOperacoes(false);
    },
    [entradaMercadoriaId, recarregarLista]
  );

  const handleAdicionarProduto = useCallback(async () => {
    if (entradaMercadoriaId) {
      setIsLoadingOperacoes(true);
      try {
        const { produto: produtoInformadoNoModal, confirmarAdicionarOutro } =
          await ModalAdicionarProduto({
            casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
            casasDecimaisValor: casasDecimais.casasDecimaisValor,
            entradaRateiaIcmsSt,
            adicionarProduto,
          });

        if (produtoInformadoNoModal) {
          await adicionarProduto(produtoInformadoNoModal);
        }

        if (confirmarAdicionarOutro) {
          handleAdicionarProduto();
        }
        setIsLoadingOperacoes(false);
      } catch (error) {
        setIsLoadingOperacoes(false);
      }
    }
  }, [
    adicionarProduto,
    casasDecimais,
    entradaMercadoriaId,
    entradaRateiaIcmsSt,
  ]);

  async function handleEditarProduto(index: number) {
    if (!entradaMercadoriaId) return;
    const entradaMercadoriaItemId = produtos[index]?.entradaMercadoriaItemId;
    const url =
      ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_OBTER_ITEM.replace(
        '{entradaMercadoriaItemId}',
        entradaMercadoriaItemId
      ).replace('{entradaMercadoriaId}', entradaMercadoriaId);

    const responseObter = await api.get<
      void,
      ResponseApi<ProdutoObterResponse>
    >(url);
    if (responseObter) {
      if (responseObter.avisos) {
        responseObter.avisos.forEach((aviso: string) => toast.warning(aviso));
        return;
      }
      if (responseObter.sucesso && responseObter.dados) {
        const produtoParaEdicao = responseObter.dados;
        const produtoTipoSimples =
          produtoParaEdicao.variacoes.length === 1 &&
          produtoParaEdicao.variacoes[0].corId === null &&
          produtoParaEdicao.variacoes[0].tamanhoId === null;

        let coresProduto = [] as CorOption[];
        let tamanhosProduto = [] as TamanhoOption[];

        if (!produtoTipoSimples) {
          const { cores, tamanhos } = await buscarVariacoesProduto(
            produtoParaEdicao.variacoes[0].produtoId
          );
          coresProduto = cores;
          tamanhosProduto = tamanhos;
        }

        const { produtoEditado } = await ModalEditarProduto({
          nomeProduto: produtoParaEdicao?.variacoes[0]?.nome,
          produtoId: produtoParaEdicao?.variacoes[0]?.produtoId,
          produtoTipoVariacao: !produtoTipoSimples,
          variacoes: produtoParaEdicao.variacoes,
          casasDecimaisQuantidade: casasDecimais.casasDecimaisQuantidade,
          casasDecimaisValor: casasDecimais.casasDecimaisValor,
          volumeUnitario: produtoParaEdicao.volumeUnitario,
          dadosEntrada: {
            custoAdicional: produtoParaEdicao.custoAdicional,
            fcpSt: produtoParaEdicao.valorFcpSt,
            icmsSt: produtoParaEdicao.valorIcmsSt,
            ipi: produtoParaEdicao.valorIpi,
            valorUnitario: produtoParaEdicao.valorUnitarioEntrada,
            quantidade: produtoTipoSimples
              ? produtoParaEdicao.quantidadeEntrada
              : 1,
          },
          entradaRateiaIcmsSt,
          cores: coresProduto,
          tamanhos: tamanhosProduto,
        });

        if (produtoEditado) {
          const response = await api.put<void, ResponseApi>(
            ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_ALTERAR_ITEM,
            {
              entradaMercadoriaItemId,
              entradaMercadoriaId,
              valorUnitarioEntrada: produtoEditado.valorUnitarioEntrada,
              valorIpi: produtoEditado.valorIpi,
              valorIcmsSt: produtoEditado.valorIcmsSt,
              valorFcpSt: produtoEditado.valorFcpSt,
              custoAdicional: produtoEditado.custoAdicional,
              produtoId: produtoEditado.produtoId,
              variacoes: produtoEditado.variacoes,
            }
          );

          if (response) {
            if (response.avisos) {
              response.avisos.forEach((aviso: string) => toast.warning(aviso));
            }

            if (response.sucesso) {
              await new Promise((resolve) => setTimeout(resolve, 100));
              // Recarregar a lista após editar produto
              await recarregarLista();
            }
          }
        }
      }
    }
  }

  async function handleRemoverProduto(index: number) {
    ModalConfirmacaoExcluir({
      title: 'Remover produto',
      text: 'Você tem certeza que deseja remover este produto da sua entrada manual?',
      submitText: 'Sim, remover',
      cancelText: 'Voltar',
      callback: async (ok: boolean) => {
        if (ok) {
          const { entradaMercadoriaItemId } = produtos[index];

          if (entradaMercadoriaItemId && entradaMercadoriaId) {
            const url =
              ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_REMOVER_ITEM.replace(
                '{entradaMercadoriaId}',
                entradaMercadoriaId
              ).replace('{entradaMercadoriaItemId}', entradaMercadoriaItemId);
            const response = await api.delete<void, ResponseApi>(url);

            if (response) {
              if (response.avisos) {
                response.avisos.map((aviso: string) => toast.warning(aviso));
              }

              if (response.sucesso) {
                await new Promise((resolve) => setTimeout(resolve, 100));
                // Recarregar a lista após remover produto
                await recarregarLista();
              }
            }
          }
        }
      },
    });
  }

  useEffect(() => {
    async function obterInformacoesProdutos() {
      if (entradaMercadoriaId) {
        setIsLoadingOperacoes(true);

        const response = await api.get<
          void,
          ResponseApi<ObterInformacoesProdutosResponse>
        >(
          ConstanteEnderecoWebservice.ENTRADA_MERCADORIA_OBTER_INFORMACOES_PRODUTOS,
          {
            params: {
              id: entradaMercadoriaId,
            },
          }
        );

        if (response) {
          if (response.avisos) {
            response.avisos.map((aviso: string) => toast.warning(aviso));
          }

          if (response.sucesso && response.dados) {
            setEntradaRateiaIcmsSt(response.dados.ratearIcmsSt);
          }
        }

        setIsLoadingOperacoes(false);
      }
    }

    obterInformacoesProdutos();
  }, [entradaMercadoriaId]);

  return (
    <>
      {isLoading && <LoadingPadrao />}

      <Container mt="6px">
        <StepDescriptionAccordion
          stepNumber={2}
          title="Lista de produtos"
          description="Clique em “adicionar itens” para formar a lista de produtos. Caso exista um novo produto será preciso cadastrá-lo na própria tela de seleção."
        />

        <Body>
          <Box
            display="flex"
            flexDirection="column"
            justifyContent="space-between"
            borderRadius="md"
            border="1px"
            bg="gray.50"
            borderColor="gray.200"
            height="calc(100vh - 375px)"
            py={{ base: 4, sm: 6, md: 6 }}
            pl={{ base: 4, sm: 6, md: 6 }}
            pr={{ base: '6px', sm: '14px', md: '24px' }}
            pt={{ base: 4, sm: '16px', md: '16px' }}
            sx={{
              '& table': { bg: 'gray.50' },
              '& thead > tr > th': {
                bg: 'gray.50',
                border: 'none',
              },
              '& td:first-of-type': {
                paddingLeft: '16px !important',
              },
              '& tbody > tr': {
                borderRadius: 'md',
                boxShadow: '0px 0px 2px #00000029',
                ...(informacoesRodape.totalProdutos > 0
                  ? {
                      border: '1px',
                      borderColor: 'gray.100',
                    }
                  : {
                      '& > td': {
                        position: 'relative',
                        _before: {
                          content: '""',
                          position: 'absolute',
                          h: 'full',
                          w: 'full',
                          top: 0,
                          left: 0,
                          borderLeft: 'none',
                          borderRight: 'none',
                          borderRadius: 'md',
                        },
                      },
                    }),
              },
              '& tbody > tr > td': {
                bg: 'white',
                lineHeight: 'none',
                _before: {
                  border:
                    informacoesRodape.totalProdutos > 0
                      ? 'none !important'
                      : '1px',
                  borderColor: 'gray.100',
                },
              },
            }}
          >
            <ListagemLancamentoManual
              produtos={produtos}
              informacoesRodape={informacoesRodape}
              buscarMaisItensNoScroll={buscarMaisItensNoScroll}
              handleToggleLinhaProduto={handleToggleLinhaProduto}
              handleEditarProduto={handleEditarProduto}
              handleRemoverProduto={handleRemoverProduto}
              handleAdicionarProduto={handleAdicionarProduto}
              foiLancadoEstoque={foiLancadoEstoque}
              isReadOnly={isReadOnly}
              casasDecimais={casasDecimais}
              isLoading={isLoading}
              isOpen={isOpen}
            />
          </Box>
        </Body>
      </Container>
      {informacoesRodape.totalProdutos > 0 && (
        <TotalizadoresFixos
          quantidadeItens={informacoesRodape.quantidadeItens}
          totalProdutos={informacoesRodape.totalProdutos}
          valorTotalProdutos={informacoesRodape.valorTotalProdutos}
        />
      )}
      <Footer
        justifyContent="space-between"
        position={isLargerThan900 ? 'fixed' : 'relative'}
        bottom="0px"
        bg="gray.50"
        borderTop={isLargerThan900 ? '1px solid' : 'none'}
        borderColor="#5502B2"
        w={`calc(100% - ${menuIsOpen ? '210px' : '108px'})`}
        py="16px"
        px="48px"
      >
        <Button
          variant="outlineDefault"
          borderRadius="full"
          w="full"
          maxW={{ base: 'full', md: '160px' }}
          onClick={previousStep}
        >
          Voltar
        </Button>
        <Stack
          w="full"
          justifyContent="flex-end"
          direction={{ base: 'column', md: 'row' }}
          spacing={{ base: 2, sm: 4, md: 6 }}
        >
          {!IsCadastroExterno && (
            <>
              {isReadOnly ? (
                <Button
                  variant="outlineDefault"
                  borderRadius="full"
                  w="full"
                  maxW={{ base: 'full', md: '196px' }}
                  onClick={voltarParaListagem}
                >
                  Voltar para a listagem
                </Button>
              ) : (
                <Button
                  variant="outlineDefault"
                  borderRadius="full"
                  w="full"
                  maxW={{ base: 'full', md: '160px' }}
                  onClick={handleDescartarEntradaMercadoria}
                  isDisabled={!temPermissaoExcluir}
                >
                  Descartar
                </Button>
              )}
            </>
          )}

          {!isReadOnly && (
            <Button
              variant="outlineDefault"
              borderRadius="full"
              w="full"
              maxW={{ base: 'full', md: '160px' }}
              onClick={voltarParaListagem}
            >
              Salvar e sair
            </Button>
          )}

          <Button
            colorScheme="purple"
            borderRadius="full"
            w="full"
            maxW={{ base: 'full', md: '160px' }}
            onClick={nextStep}
            isDisabled={produtos.length === 0}
          >
            Avançar
          </Button>
        </Stack>
      </Footer>
    </>
  );
}
