import {
  Box,
  Text,
  Icon,
  <PERSON><PERSON>tack,
  <PERSON>lex,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
} from '@chakra-ui/react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FiCheckCircle, FiChevronUp } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import { usePadronizacaoContext } from 'store/Padronizacao/Padronizacao';

import { ProdutoOptionProps } from 'pages/EntradaMercadoria/EntradaManual/LancamentoProdutos/ModalAdicionarProduto/validationForm';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';

import {
  Produto,
  EntradaMercadoriaStatusVinculoProduto,
} from '../hooks/useProdutosVinculacao';
import { TextoTooltip } from '../TextoTooltip';

import { DetalhesItemProduto } from './DetalhesItemProduto';

interface ListagemProdutosProps {
  handleToggleLinhaProduto: (id: string) => void;
  handleEditar: (index: number) => Promise<void>;
  handleVincularProduto: (
    index: number,
    produtoPendenteVariacoes?: ProdutoOptionProps
  ) => Promise<void>;
  modoTelaCheia?: boolean;
  itensListagem: Produto[];
  isLoading: boolean;
  buscarMaisItensNoScroll: (element: HTMLDivElement) => void;
  obterCorBackground: (status: EntradaMercadoriaStatusVinculoProduto) => string;
  isOpen: (id: string) => boolean;
}

export function ListagemProdutos({
  handleToggleLinhaProduto,
  handleEditar,
  handleVincularProduto,
  modoTelaCheia = false,
  itensListagem,
  isLoading,
  buscarMaisItensNoScroll,
  obterCorBackground,
  isOpen,
}: ListagemProdutosProps) {
  const { casasDecimais } = usePadronizacaoContext();

  const [scrollEl, setScrollEl] = useState<HTMLDivElement | null>(null);

  const rowVirtualizer = useVirtualizer({
    count: itensListagem?.length ?? 0,
    getScrollElement: () => scrollEl,
    estimateSize: (index) =>
      isOpen(itensListagem[index]?.documentoFiscalItemId) ? 130 : 64,
    overscan: 5,
    getItemKey: (index) => itensListagem[index]?.documentoFiscalItemId ?? index,
    measureElement: (el) => el.getBoundingClientRect().height,
  });

  const measureRows = useCallback(() => {
    requestAnimationFrame(() => rowVirtualizer.measure());
  }, [rowVirtualizer]);

  const colunas = useMemo<ColumnDef<Produto, any>[]>(
    () => [
      {
        id: 'descricaoProdutoNota',
        header: 'Produto',
        size: 800,
        cell: ({ row }) => {
          const produto = row.original;

          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const statusQuePodemMostrarDetalhes = [
            EntradaMercadoriaStatusVinculoProduto.VINCULADO,
            EntradaMercadoriaStatusVinculoProduto.PENDENTE_INFORMAR_VARIACOES,
          ];
          const podeMostrarDetalhes =
            statusQuePodemMostrarDetalhes.includes(produto.statusVinculo) ||
            !!produto.dadosAdicionais;

          return (
            <Box
              cursor={podeMostrarDetalhes ? 'pointer' : 'default'}
              userSelect="none"
              fontSize="14px"
              onClick={() => {
                if (podeMostrarDetalhes) {
                  handleToggleLinhaProduto(produto.documentoFiscalItemId);
                  measureRows();
                }
              }}
              color={produtoEstaVinculado ? 'white' : 'inherit'}
              transition="all 0.3s"
            >
              <Button
                tabIndex={0}
                bg="transparent"
                p="4px"
                pb="0px"
                mr="6px"
                h="fit-content"
                borderRadius="6px"
                _focus={{ background: 'gray.100' }}
                onClick={(e) => {
                  e.stopPropagation();
                  handleToggleLinhaProduto(produto.documentoFiscalItemId);
                  measureRows();
                }}
                minW="16px"
                opacity={podeMostrarDetalhes ? '1' : '0'}
                pointerEvents={podeMostrarDetalhes ? 'all' : 'none'}
              >
                <Icon
                  as={FiChevronUp}
                  mb="6px"
                  transform={
                    isOpen(produto.documentoFiscalItemId)
                      ? ''
                      : 'rotate(180deg)'
                  }
                  role="button"
                  transition="all 0.3s"
                />
              </Button>
              {produto.descricaoProdutoNota}
              {isOpen(produto.documentoFiscalItemId) &&
                produto.dadosAdicionais && (
                  <Flex
                    w="97%"
                    flexDir="row"
                    align="center"
                    pl="30px"
                    mt="4px"
                    gap="4px"
                    fontSize="12px"
                    fontWeight="bold"
                  >
                    <TextoTooltip
                      texto={produto.dadosAdicionais}
                      maxWidth="100%"
                    />
                  </Flex>
                )}

              {isOpen(produto.documentoFiscalItemId) && (
                <DetalhesItemProduto
                  produto={produto}
                  obterCorBackground={obterCorBackground}
                />
              )}
            </Box>
          );
        },
      },
      {
        id: 'quantidade',
        header: 'Quantidade',
        size: 120,
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(
                produto.quantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Text>
          );
        },
      },
      {
        id: 'valorUnitario',
        header: 'Valor unitário',
        size: 140,
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              textAlign="right"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(
                produto.valorUnitario,
                casasDecimais.casasDecimaisValor
              )}
            </Text>
          );
        },
        meta: {
          textAlign: 'right',
        },
      },
      {
        id: 'valorTotal',
        header: 'Valor total',
        size: 120,
        cell: ({ row }) => {
          const produto = row.original;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          return (
            <Text
              fontSize="14px"
              textAlign="right"
              color={produtoEstaVinculado ? 'white' : 'inherit'}
            >
              {DecimalMask(produto.valorTotal, 2, 2)}
            </Text>
          );
        },
        meta: {
          textAlign: 'right',
        },
      },
      {
        id: 'acoes',
        header: 'Ações',
        size: 180,
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;
          const produtoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.VINCULADO;
          const produtoNaoEstaVinculado =
            produto.statusVinculo ===
            EntradaMercadoriaStatusVinculoProduto.NAO_VINCULADO;

          const handleVincularClick = () => {
            if (produtoNaoEstaVinculado) {
              handleVincularProduto(index);
              return;
            }

            handleVincularProduto(index, {
              id: produto.produtoVinculado?.id || '',
              nome: produto.produtoVinculado?.nome || '',
              tipoProduto: produto.produtoVinculado?.tipoProduto || 2,
              volumeUnitario: produto.produtoVinculado?.volumeUnitario || false,
              referencia: produto.produtoVinculado?.referencia || '',
              precoCompra: produto.produtoVinculado?.precoCompra || 0,
              coresOptions: [],
              tamanhosOptions: [],
            });
          };

          if (produtoEstaVinculado) {
            return (
              <Flex justifyContent="flex-end" gap="12px" alignItems="center">
                <HStack spacing="1" color="secondary.300">
                  <Icon as={FiCheckCircle} boxSize="4" />
                  <Text fontSize="xs">Vinculado</Text>
                </HStack>
                <ActionsMenu
                  colorScheme="white"
                  backgroundHoverColor="gray.500"
                  menuZIndex="popover"
                  items={[
                    {
                      content: 'Editar',
                      onClick: () => handleEditar(index),
                    },
                  ]}
                />
              </Flex>
            );
          }

          return (
            <Flex alignItems="center" justifyContent="flex-end">
              <Button
                size="xs"
                colorScheme="orange"
                minW="136px"
                onClick={handleVincularClick}
              >
                {produtoNaoEstaVinculado
                  ? 'Vincular ao sistema'
                  : 'Informar variações'}
              </Button>
            </Flex>
          );
        },
        meta: {
          textAlign: 'right',
          paddingRight: '28px !important',
        },
      },
    ],
    [
      isOpen,
      obterCorBackground,
      handleToggleLinhaProduto,
      measureRows,
      casasDecimais.casasDecimaisQuantidade,
      casasDecimais.casasDecimaisValor,
      handleVincularProduto,
      handleEditar,
    ]
  );

  const table = useReactTable({
    data: itensListagem,
    columns: colunas,
    getCoreRowModel: getCoreRowModel(),
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  useEffect(() => {
    rowVirtualizer.measure();
  }, [modoTelaCheia, itensListagem.length, rowVirtualizer]);

  return (
    <>
      {isLoading && modoTelaCheia && <LoadingPadrao />}
      <Box
        position="relative"
        display="flex"
        flexDirection="column"
        justifyContent="space-between"
        borderRadius="md"
        border={modoTelaCheia ? 'none' : '1px'}
        bg="gray.50"
        height={modoTelaCheia ? 'calc(100vh - 25px)' : 'calc(100vh - 375px)'}
        borderColor="gray.200"
        py={modoTelaCheia ? '12px' : { base: 4, sm: 6, md: 6 }}
        pl={modoTelaCheia ? '12px' : { base: 4, sm: 6, md: 6 }}
        pb={modoTelaCheia ? '60px' : 0}
        pr={modoTelaCheia ? '12px' : { base: '6px', sm: '14px', md: '24px' }}
        sx={{
          '& table': { bg: 'gray.50' },
          '& thead > tr > th': {
            bg: 'gray.50',
            border: 'none',
            padding: '16px ',
          },
          '& td:first-of-type': {
            paddingLeft: '16px !important',
          },
          '& thead > tr > th:first-of-type': {
            paddingLeft: '44px !important',
          },
          '& tbody > tr': {
            borderRadius: 'md',
          },
          '& tbody > tr > td': {
            padding: '16px !important',
            lineHeight: 'none',
          },
        }}
      >
        <Box
          ref={setScrollEl}
          onScroll={(event) => buscarMaisItensNoScroll(event.currentTarget)}
          overflow="auto"
          transition="height 0.3s"
          position="relative"
        >
          <Table
            variant="unstyled"
            size="sm"
            style={{ tableLayout: 'fixed', width: '100%' }}
          >
            <Thead position="sticky" top={0} bg="white" zIndex={1}>
              {table.getHeaderGroups().map((headerGroup) => (
                <Tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <Th
                      key={header.id}
                      {...header.column.columnDef.meta}
                      width={`${header.getSize()}px`}
                      minWidth={`${header.getSize()}px`}
                      maxWidth={`${header.getSize()}px`}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </Th>
                  ))}
                </Tr>
              ))}
            </Thead>
          </Table>
          <Box
            width="100%"
            height={`${rowVirtualizer.getTotalSize()}px`}
            position="relative"
          >
            {virtualItems.map((virtualItem) => {
              const row = table.getRowModel().rows[virtualItem.index];
              if (!row) return null;

              return (
                <Box
                  key={virtualItem.key}
                  position="absolute"
                  top={0}
                  left={0}
                  width="100%"
                  ref={rowVirtualizer.measureElement}
                  transform={`translateY(${virtualItem.start}px)`}
                >
                  <Table style={{ tableLayout: 'fixed', width: '100%' }}>
                    <Tbody>
                      <Tr>
                        {row.getVisibleCells().map((cell) => (
                          <Td
                            key={cell.id}
                            {...cell.column.columnDef.meta}
                            width={`${cell.column.getSize()}px`}
                            minWidth={`${cell.column.getSize()}px`}
                            maxWidth={`${cell.column.getSize()}px`}
                            overflow="hidden"
                            textOverflow={
                              cell.column.id === 'descricaoProdutoNota'
                                ? 'unset'
                                : 'ellipsis'
                            }
                            whiteSpace={
                              cell.column.id === 'descricaoProdutoNota'
                                ? 'normal'
                                : 'nowrap'
                            }
                            bg={obterCorBackground(row.original.statusVinculo)}
                            color={
                              row.original.statusVinculo ===
                              EntradaMercadoriaStatusVinculoProduto.VINCULADO
                                ? 'white'
                                : 'inherit'
                            }
                          >
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </Td>
                        ))}
                      </Tr>
                    </Tbody>
                  </Table>
                </Box>
              );
            })}
          </Box>

          {itensListagem.length === 0 && !isLoading && (
            <Box p={6} textAlign="center">
              <Text color="gray.500">Nenhum produto encontrado</Text>
            </Box>
          )}
        </Box>
      </Box>
    </>
  );
}
