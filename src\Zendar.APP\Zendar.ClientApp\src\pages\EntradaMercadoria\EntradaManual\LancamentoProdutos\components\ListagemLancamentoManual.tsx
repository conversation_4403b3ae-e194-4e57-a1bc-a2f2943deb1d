import {
  Tr,
  Td,
  Flex,
  Icon,
  Box,
  Divider,
  HStack,
  Text,
  Button,
  Table,
  Thead,
  Tbody,
  Th,
} from '@chakra-ui/react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  ColumnDef,
} from '@tanstack/react-table';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { FiChevronUp } from 'react-icons/fi';

import { DecimalMask } from 'helpers/format/fieldsMasks';

import LoadingPadrao from 'components/Layout/Loading/LoadingPadrao';
import { ActionsMenu } from 'components/update/Table/ActionsMenu';
import { InfoTooltip } from 'components/update/Tooltip/InfoTooltip';

import { Produto, InformacoesRodape } from '../types';

type ListagemLancamentoManualProps = {
  produtos: Produto[];
  informacoesRodape: InformacoesRodape;
  buscarMaisItensNoScroll: () => Promise<void>;
  handleToggleLinhaProduto: (index: number) => void;
  handleEditarProduto: (index: number) => void;
  handleRemoverProduto: (index: number) => void;
  foiLancadoEstoque: boolean;
  isReadOnly: boolean;
  casasDecimais: {
    casasDecimaisQuantidade: number;
    casasDecimaisValor: number;
  };
  isLoading?: boolean;
  modoTelaCheia?: boolean;
  isOpen: (produtoId: string) => boolean;
};

export const ListagemLancamentoManual = ({
  produtos,
  informacoesRodape,
  buscarMaisItensNoScroll,
  handleToggleLinhaProduto,
  handleEditarProduto,
  handleRemoverProduto,
  foiLancadoEstoque,
  isReadOnly,
  casasDecimais,
  isLoading = false,
  modoTelaCheia = false,
  isOpen,
}: ListagemLancamentoManualProps) => {
  const [scrollEl, setScrollEl] = useState<HTMLDivElement | null>(null);

  const rowVirtualizer = useVirtualizer({
    count: produtos?.length ?? 0,
    getScrollElement: () => scrollEl,
    estimateSize: (index) =>
      isOpen(produtos[index]?.entradaMercadoriaItemId ?? '') ? 115 : 64,
    overscan: 5,
    getItemKey: (index) => produtos[index]?.entradaMercadoriaItemId ?? index,
    measureElement: (el) => el.getBoundingClientRect().height,
  });

  const measureRows = useCallback(() => {
    rowVirtualizer.measure();
  }, [rowVirtualizer]);

  const colunas = useMemo<ColumnDef<Produto>[]>(
    () => [
      {
        id: 'nomeProduto',
        header: 'Produto',
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;

          return (
            <Td
              cursor="pointer"
              userSelect="none"
              fontSize="14px"
              onClick={() => {
                measureRows();
                handleToggleLinhaProduto(index);
              }}
            >
              <Flex alignItems="center" gap="8px">
                <Button
                  tabIndex={0}
                  bg="transparent"
                  p="4px"
                  pb="0px"
                  mr="6px"
                  h="fit-content"
                  borderRadius="6px"
                  _focus={{
                    background: 'gray.100',
                  }}
                  minW="16px"
                >
                  <Icon
                    as={FiChevronUp}
                    mb="6px"
                    transform={produto.isOpen ? '' : 'rotate(180deg)'}
                    role="button"
                    transition="all 0.3s"
                  />
                </Button>

                <Text overflowWrap="anywhere" noOfLines={2}>
                  {produto.nomeProduto}
                </Text>
              </Flex>
            </Td>
          );
        },
      },
      {
        id: 'quantidade',
        header: 'Quantidade',
        cell: ({ row }) => {
          const produto = row.original;

          return (
            <Td fontSize="14px">
              {DecimalMask(
                produto.quantidade,
                casasDecimais.casasDecimaisQuantidade
              )}
            </Td>
          );
        },
      },
      {
        id: 'valorUnitarioEntrada',
        header: 'Valor Unitário',
        cell: ({ row }) => {
          const produto = row.original;

          return (
            <Td isNumeric fontSize="14px">
              {DecimalMask(
                produto.valorUnitarioEntrada,
                casasDecimais.casasDecimaisValor
              )}
            </Td>
          );
        },
      },
      {
        id: 'valorTotal',
        header: 'Valor Total',
        cell: ({ row }) => {
          const produto = row.original;

          return (
            <Td isNumeric fontSize="14px">
              {DecimalMask(produto.valorTotal, 2, 2)}
            </Td>
          );
        },
      },
      {
        id: 'acoes',
        header: 'Ações',
        cell: ({ row }) => {
          const produto = row.original;
          const index = row.index;

          return (
            <Td>
              <Flex justify="end">
                <ActionsMenu
                  isDisabled={
                    foiLancadoEstoque || isReadOnly || produto.bloquearAlteracao
                  }
                  items={[
                    {
                      content: 'Editar',
                      onClick: () => {
                        handleEditarProduto(index);
                      },
                    },
                    {
                      content: 'Remover',
                      onClick: () => {
                        handleRemoverProduto(index);
                      },
                    },
                  ]}
                />
              </Flex>
            </Td>
          );
        },
      },
    ],
    [
      measureRows,
      casasDecimais.casasDecimaisQuantidade,
      casasDecimais.casasDecimaisValor,
      handleToggleLinhaProduto,
      handleEditarProduto,
      handleRemoverProduto,
      foiLancadoEstoque,
      isReadOnly,
    ]
  );

  const table = useReactTable({
    data: produtos,
    columns: colunas,
    getCoreRowModel: getCoreRowModel(),
  });

  const virtualItems = rowVirtualizer.getVirtualItems();

  useEffect(() => {
    rowVirtualizer.measure();
  }, [modoTelaCheia, produtos.length, rowVirtualizer]);

  // Detectar quando chegou próximo ao final para carregar mais itens
  useEffect(() => {
    const [lastItem] = [...virtualItems].reverse();

    if (!lastItem) {
      return;
    }

    if (
      lastItem.index >= produtos.length - 1 &&
      produtos.length < informacoesRodape.totalProdutos &&
      !isLoading
    ) {
      buscarMaisItensNoScroll();
    }
  }, [
    virtualItems,
    produtos.length,
    informacoesRodape.totalProdutos,
    isLoading,
    buscarMaisItensNoScroll,
  ]);

  if (isLoading && produtos.length === 0) {
    return <LoadingPadrao />;
  }

  return (
    <Box bg="gray.50" borderRadius="md" overflow="auto" position="relative">
      <Table variant="simple" size="sm">
        <Thead bg="white" position="sticky" top={0} zIndex={1}>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => (
                <Th
                  key={header.id}
                  fontSize="12px"
                  fontWeight="600"
                  color="gray.600"
                  textTransform="uppercase"
                  letterSpacing="0.5px"
                  py="12px"
                  px="16px"
                  borderBottom="1px solid"
                  borderColor="gray.200"
                >
                  {header.isPlaceholder
                    ? null
                    : flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                </Th>
              ))}
            </Tr>
          ))}
        </Thead>
      </Table>

      <Box
        height={`${rowVirtualizer.getTotalSize()}px`}
        width="100%"
        position="relative"
      >
        {virtualItems.map((virtualItem) => {
          const produto = produtos[virtualItem.index];
          const isExpanded = isOpen(produto?.entradaMercadoriaItemId ?? '');

          return (
            <Box
              key={virtualItem.key}
              position="absolute"
              top={0}
              left={0}
              width="100%"
              height={`${virtualItem.size}px`}
              transform={`translateY(${virtualItem.start}px)`}
            >
              <Table style={{ tableLayout: 'fixed', width: '100%' }}>
                <Tbody>
                  <Tr
                    transition="all 0.3s"
                    sx={
                      isExpanded
                        ? {
                            '& > td': {
                              marginBottom: '5px',
                              borderBottomRadius: '0px !important',
                            },
                          }
                        : {}
                    }
                  >
                    <Td
                      cursor="pointer"
                      userSelect="none"
                      fontSize="14px"
                      onClick={() => {
                        measureRows();
                        handleToggleLinhaProduto(virtualItem.index);
                      }}
                    >
                      <Flex alignItems="center" gap="8px">
                        <Button
                          tabIndex={0}
                          bg="transparent"
                          p="4px"
                          pb="0px"
                          mr="6px"
                          h="fit-content"
                          borderRadius="6px"
                          _focus={{
                            background: 'gray.100',
                          }}
                          minW="16px"
                        >
                          <Icon
                            as={FiChevronUp}
                            mb="6px"
                            transform={isExpanded ? '' : 'rotate(180deg)'}
                            role="button"
                            transition="all 0.3s"
                          />
                        </Button>

                        <Text overflowWrap="anywhere" noOfLines={2}>
                          {produto.nomeProduto}
                        </Text>
                      </Flex>
                    </Td>
                    <Td fontSize="14px">
                      {DecimalMask(
                        produto.quantidade,
                        casasDecimais.casasDecimaisQuantidade
                      )}
                    </Td>
                    <Td isNumeric fontSize="14px">
                      {DecimalMask(
                        produto.valorUnitarioEntrada,
                        casasDecimais.casasDecimaisValor
                      )}
                    </Td>
                    <Td isNumeric fontSize="14px">
                      {DecimalMask(produto.valorTotal, 2, 2)}
                    </Td>
                    <Td>
                      <Flex justify="end">
                        <ActionsMenu
                          isDisabled={
                            foiLancadoEstoque ||
                            isReadOnly ||
                            produto.bloquearAlteracao
                          }
                          items={[
                            {
                              content: 'Editar',
                              onClick: () => {
                                handleEditarProduto(virtualItem.index);
                              },
                            },
                            {
                              content: 'Remover',
                              onClick: () => {
                                handleRemoverProduto(virtualItem.index);
                              },
                            },
                          ]}
                        />
                      </Flex>
                    </Td>
                  </Tr>
                </Tbody>
              </Table>

              {isExpanded && (
                <Box
                  h="52px"
                  // borderBottomRadius="md"
                  bg="white"
                  px="5"
                  transition="all 0.3s"
                  // mt="-2px"
                >
                  <HStack
                    spacing="6"
                    px="5"
                    pl="26px"
                    h="full"
                    lineHeight="none"
                    fontSize="xs"
                  >
                    <Flex>
                      <Text fontWeight="light">ICMS ST:</Text>
                      <Text ml="2" fontWeight="bold">
                        <Text as="span" fontSize="2xs" mr="0.5">
                          R$
                        </Text>
                        {DecimalMask(produto.valorIcmsSt, 2, 2)}
                      </Text>
                    </Flex>
                    <Divider orientation="vertical" h="6" />
                    <Flex>
                      <Text fontWeight="light">IPI:</Text>
                      <Text ml="2" fontWeight="bold">
                        <Text as="span" fontSize="2xs" mr="0.5">
                          R$
                        </Text>
                        {DecimalMask(produto.valorIpi, 2, 2)}
                      </Text>
                    </Flex>
                    <Divider orientation="vertical" h="6" />
                    <Flex>
                      <Text fontWeight="light">FCP ST:</Text>
                      <Text ml="2" fontWeight="bold">
                        <Text as="span" fontSize="2xs" mr="0.5">
                          R$
                        </Text>
                        {DecimalMask(produto.valorFcpSt, 2, 2)}
                      </Text>
                    </Flex>
                    <Divider orientation="vertical" h="6" />
                    <Flex h="center" alignItems="center">
                      <Text fontWeight="light">Custo adicional:</Text>
                      <Text ml="2" fontWeight="bold">
                        <Text as="span" fontSize="2xs" mr="0.5">
                          R$
                        </Text>
                        {DecimalMask(produto.custoAdicional, 2, 2)}
                      </Text>

                      <Box ml="2">
                        <InfoTooltip
                          label="Os valores deste campo não serão somados ao valor total da entrada, servindo apenas para compor o custo do produto."
                          tabIndex={-1}
                        />
                      </Box>
                    </Flex>
                  </HStack>
                </Box>
              )}
            </Box>
          );
        })}

        {isLoading && produtos.length > 0 && (
          <Box
            position="absolute"
            bottom="0"
            left="0"
            right="0"
            bg="rgba(255, 255, 255, 0.8)"
            p="2"
            textAlign="center"
          >
            <Text fontSize="sm" color="gray.600">
              Carregando mais produtos...
            </Text>
          </Box>
        )}
      </Box>

      {produtos.length === 0 && !isLoading && (
        <Box p="8" textAlign="center">
          <Text color="gray.500">Nenhum produto adicionado.</Text>
        </Box>
      )}
    </Box>
  );
};
